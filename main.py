import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error
import numpy as np

def train_and_evaluate_model(model, X_train, y_train, X_test, y_test, model_name="Model"):
    """
    Trains a given model and evaluates its performance.

    Args:
        model: The machine learning model pipeline to train.
        X_train: Training features.
        y_train: Training target.
        X_test: Testing features.
        y_test: Testing target.
        model_name (str): The name of the model for printing results.
    """
    print(f"--- {model_name} ---")
    
    # Train the model
    model.fit(X_train, y_train)
    
    # Make predictions on the test set
    y_pred = model.predict(X_test)
    
    # Evaluate the model
    r2 = r2_score(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    
    print(f"R-squared (R2): {r2:.4f}")
    print(f"Root Mean Squared Error (RMSE): ${rmse:.2f}")
    print("-" * (len(model_name) + 8) + "\n")
    
    return model

def main():
    """
    Main function to execute the machine learning workflow.
    """
    # --- 1. Load Data ---
    try:
        data = pd.read_csv('insurance.csv')
        print("Dataset loaded successfully.")
        print("First 5 rows of the dataset:")
        print(data.head())
        print("\n")
    except FileNotFoundError:
        print("Error: 'insurance.csv' not found.")
        print("Please download the dataset and place it in the same directory as this script.")
        return

    # --- 2. Data Preparation ---
    # Separate features (X) and target variable (y)
    X = data.drop('charges', axis=1)
    y = data['charges']

    # Identify numerical and categorical features
    numerical_features = X.select_dtypes(include=['int64', 'float64']).columns
    categorical_features = X.select_dtypes(include=['object']).columns

    # Split the data into training and testing sets (80% train, 20% test)
    # We use random_state for reproducibility
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # --- 3. Preprocessing ---
    # Create a preprocessing pipeline for numerical and categorical features.
    # StandardScaler: Scales numerical features to have a mean of 0 and variance of 1.
    # OneHotEncoder: Converts categorical features into a numerical format.
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', StandardScaler(), numerical_features),
            ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
        ])

    # --- 4. Model Training and Evaluation ---
    
    # Model 1: Linear Regression (a good baseline model)
    lr_pipeline = Pipeline(steps=[
        ('preprocessor', preprocessor),
        ('regressor', LinearRegression())
    ])
    
    trained_lr_model = train_and_evaluate_model(lr_pipeline, X_train, y_train, X_test, y_test, "Linear Regression")

    # Model 2: Random Forest Regressor (a more powerful ensemble model)
    rf_pipeline = Pipeline(steps=[
        ('preprocessor', preprocessor),
        ('regressor', RandomForestRegressor(random_state=42))
    ])
    
    trained_rf_model = train_and_evaluate_model(rf_pipeline, X_train, y_train, X_test, y_test, "Random Forest Regressor")

    # --- 5. Prediction on New Data ---
    print("--- Prediction on a New Sample ---")
    # Create a sample data point as a pandas DataFrame
    # The column names must match the original dataset
    new_customer = pd.DataFrame({
        'age': [31],
        'sex': ['male'],
        'bmi': [26.2],
        'children': [0],
        'smoker': ['no'],
        'region': ['northwest']
    })

    print("New customer data:")
    print(new_customer)

    # Use the trained Random Forest model (which performed better) to predict
    predicted_charge = trained_rf_model.predict(new_customer)
    
    print(f"\nPredicted Insurance Cost for the new customer: ${predicted_charge[0]:.2f}")
    print("-" * 36)


if __name__ == "__main__":
    main()